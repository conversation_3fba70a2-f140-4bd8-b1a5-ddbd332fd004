# 📚 دليل التشغيل الشامل - نظام إدارة الكتب المتكامل

## 🎯 نظرة عامة

نظام إدارة الكتب المتكامل هو تطبيق Microsoft Access احترافي مصمم خصيصاً لإدارة الكتب والوثائق الرسمية بطريقة حديثة وفعالة. يوفر النظام 9 أقسام رئيسية مع واجهة مستخدم عصرية وسهلة الاستخدام.

## 📁 ملفات النظام

```
نظام إدارة الكتب/
├── BookManagementSystem.accdb     # قاعدة البيانات الرئيسية
├── DatabaseStructure.sql          # هيكل قاعدة البيانات
├── VBA_Modules.txt                # وحدات البرمجة
├── Forms_Design.txt               # تصميم النماذج
├── Reports_Design.txt             # تصميم التقارير
└── دليل_التشغيل_الشامل.md         # هذا الدليل
```

## 🚀 خطوات التشغيل الأولى

### 1. إنشاء قاعدة البيانات
1. افتح Microsoft Access
2. اختر "قاعدة بيانات فارغة"
3. احفظ باسم "BookManagementSystem.accdb"
4. انسخ محتوى ملف `DatabaseStructure.sql` ونفذه في Access

### 2. إنشاء الوحدات البرمجية
1. اذهب إلى تبويب "إنشاء" → "وحدة نمطية"
2. انسخ محتوى `VBA_Modules.txt`
3. احفظ الوحدات بالأسماء المحددة

### 3. إنشاء النماذج
1. استخدم معالج النماذج أو أنشئ نماذج فارغة
2. اتبع التصميم المحدد في `Forms_Design.txt`
3. أضف الكود المطلوب لكل نموذج

### 4. إنشاء التقارير
1. استخدم معالج التقارير
2. اتبع التصميم في `Reports_Design.txt`
3. أنشئ الاستعلامات المطلوبة

## 🏗️ الأقسام الرئيسية

### 1. 🏠 لوحة المعلومات
**الوظيفة**: عرض إحصائيات شاملة ونظرة عامة على النظام

**المميزات**:
- 4 بطاقات إحصائية ملونة:
  - 🔵 إجمالي الكتب
  - 🟢 كتب جديدة اليوم  
  - 🟠 في الأرشيف
  - 🔴 تحتاج متابعة
- جدول الأنشطة الأخيرة
- رسم بياني للإحصائيات
- تحديث تلقائي للبيانات

**كيفية الاستخدام**:
1. تظهر لوحة المعلومات تلقائياً عند فتح النظام
2. تتحدث الإحصائيات تلقائياً
3. انقر على أي إحصائية للحصول على تفاصيل أكثر

### 2. 📚 إدارة الكتب
**الوظيفة**: إدارة شاملة لجميع الكتب والوثائق

**الحقول المطلوبة**:
- ✅ رقم الكتاب (توليد تلقائي متاح)
- ✅ تاريخ الكتاب
- ✅ الموضوع
- ✅ الجهة المرسل إليها
- ✅ جهة التنفيذ
- ✅ التفاصيل
- ✅ صورة الكتاب الممسوح

**العمليات المتاحة**:
- ➕ إضافة كتاب جديد
- ✏️ تعديل البيانات
- 🗑️ حذف الكتب
- 👁️ عرض التفاصيل
- 🖼️ إضافة/عرض الصور
- 📄 طباعة الكتاب
- 📦 أرشفة الكتاب

**خطوات إضافة كتاب جديد**:
1. انقر على "كتاب جديد"
2. سيتم توليد رقم الكتاب تلقائياً
3. أدخل التاريخ (افتراضياً تاريخ اليوم)
4. اكتب موضوع الكتاب
5. اختر الجهة المرسل إليها من القائمة
6. اختر جهة التنفيذ
7. أضف التفاصيل إذا لزم الأمر
8. أضف صورة الكتاب (اختياري)
9. انقر "حفظ"

### 3. 📷 المسح الضوئي
**الوظيفة**: تحويل الوثائق الورقية إلى رقمية

**المميزات المتقدمة**:
- 🖱️ سحب وإفلات الصور
- 📸 مسح متعدد للصور
- 📄 إنشاء ملفات PDF
- 🔍 معاينة الصور قبل الحفظ
- 🗜️ ضغط وتحسين الصور
- 📊 شريط تقدم للعمليات

**خطوات المسح الضوئي**:
1. انقر على "المسح الضوئي"
2. اسحب الصور إلى منطقة المسح أو انقر "اختيار صورة"
3. يمكنك اختيار عدة صور معاً
4. معاينة الصور المحددة
5. انقر "بدء المسح" لمعالجة الصور
6. اختر "حفظ كـ PDF" لإنشاء ملف PDF
7. احفظ الصور في قاعدة البيانات

**تنسيقات الصور المدعومة**:
- JPG/JPEG
- PNG
- BMP
- GIF

### 4. 🔍 البحث المتقدم
**الوظيفة**: بحث دقيق ومتقدم في قاعدة البيانات

**معايير البحث المتاحة**:
- 🔢 رقم الكتاب (بحث جزئي أو كامل)
- 📅 نطاق التواريخ (من - إلى)
- 📝 الموضوع (بحث نصي)
- 🏢 الجهة المرسل إليها
- ⚙️ جهة التنفيذ
- 📊 الحالة (جديد، قيد التنفيذ، مكتمل، مؤجل)

**مميزات البحث**:
- 🔍 بحث جزئي ومطابق تام
- 🎯 فلترة متعددة المعايير
- 📋 عرض النتائج في جدول منسق
- 📤 تصدير نتائج البحث
- 💾 حفظ معايير البحث المفضلة

**خطوات البحث المتقدم**:
1. أدخل معايير البحث المطلوبة
2. يمكنك استخدام معيار واحد أو عدة معايير
3. انقر "بحث" لعرض النتائج
4. انقر على أي نتيجة لعرض التفاصيل
5. استخدم "تصدير النتائج" لحفظ النتائج
6. انقر "مسح المعايير" لبحث جديد

### 5. 📈 التقارير
**الوظيفة**: إنشاء تقارير مفصلة ومخصصة

**أنواع التقارير المتاحة**:

#### 📊 التقرير الشهري
- عرض جميع كتب شهر محدد
- إحصائيات الشهر
- توزيع الكتب حسب الحالة
- رسم بياني للأنشطة

#### 📅 التقرير السنوي  
- ملخص السنة الكاملة
- توزيع الكتب حسب الأشهر
- مقارنة مع السنوات السابقة
- اتجاهات النمو

#### 🏢 تقرير حسب الجهة
- جميع كتب جهة محددة
- الكتب المرسلة إلى الجهة
- الكتب المنفذة بواسطة الجهة
- إحصائيات الأداء

#### 📋 تقرير الأنشطة
- سجل جميع أنشطة النظام
- أنشطة المستخدمين
- العمليات المنفذة
- التوقيتات والتفاصيل

**خطوات إنشاء التقارير**:
1. اختر نوع التقرير المطلوب
2. حدد المعايير (الشهر، السنة، الجهة)
3. انقر "إنشاء التقرير"
4. معاينة التقرير قبل الطباعة
5. طباعة أو تصدير التقرير (PDF/Excel)

### 6. 🗄️ الأرشيف
**الوظيفة**: إدارة الكتب المؤرشفة والمحفوظات

**المميزات**:
- 📦 نقل الكتب للأرشيف
- 🔄 استرجاع من الأرشيف
- 🔐 صلاحيات الوصول
- 🏷️ فهرسة متقدمة
- 🔍 بحث في الأرشيف

**عمليات الأرشفة**:
1. **أرشفة كتاب**:
   - اختر الكتاب من القائمة
   - انقر "أرشفة"
   - أضف سبب الأرشفة
   - تأكيد العملية

2. **استرجاع من الأرشيف**:
   - ابحث في الأرشيف
   - اختر الكتاب المطلوب
   - انقر "استرجاع"
   - تأكيد العملية

### 7. 👥 إدارة المستخدمين
**الوظيفة**: إدارة حسابات وصلاحيات المستخدمين

**أنواع المستخدمين**:
- 👑 **مدير**: صلاحيات كاملة
- 👤 **مستخدم**: صلاحيات محدودة
- 👁️ **مراقب**: عرض فقط

**العمليات المتاحة**:
- ➕ إضافة مستخدمين جدد
- ✏️ تعديل بيانات المستخدمين
- 🔐 تغيير كلمات المرور
- 🚫 تعطيل/تفعيل الحسابات
- 📊 تتبع أنشطة المستخدمين

**خطوات إضافة مستخدم جديد**:
1. انقر "مستخدم جديد"
2. أدخل اسم المستخدم (فريد)
3. أدخل كلمة المرور
4. اكتب الاسم الكامل
5. أدخل البريد الإلكتروني
6. اختر نوع المستخدم
7. انقر "حفظ"

### 8. 💾 النسخ الاحتياطي
**الوظيفة**: حماية البيانات والنسخ الاحتياطي

**أنواع النسخ الاحتياطية**:
- 🔄 **نسخ تلقائية**: حسب الجدولة المحددة
- 📱 **نسخ يدوية**: عند الطلب
- 🌐 **نسخ خارجية**: على أجهزة أخرى

**المميزات**:
- 📅 جدولة النسخ التلقائية
- 🗜️ ضغط الملفات لتوفير المساحة
- ✅ التحقق من سلامة النسخ
- 📊 تقارير النسخ الاحتياطية

**خطوات إنشاء نسخة احتياطية**:
1. انقر "إنشاء نسخة احتياطية"
2. اختر موقع الحفظ
3. أضف وصف للنسخة
4. انقر "بدء النسخ"
5. انتظار اكتمال العملية

**خطوات استعادة نسخة احتياطية**:
1. اختر النسخة من القائمة
2. انقر "استعادة"
3. تأكيد العملية (تحذير: سيتم استبدال البيانات الحالية)
4. انتظار اكتمال الاستعادة

### 9. ⚙️ الإعدادات
**الوظيفة**: تخصيص النظام حسب احتياجاتك

**أقسام الإعدادات**:

#### 🎨 إعدادات العرض
- اسم النظام
- شعار المؤسسة
- ألوان الواجهة
- حجم الخط

#### 👤 تفضيلات المستخدم
- اللغة المفضلة
- تنسيق التاريخ
- عدد السجلات في الصفحة
- الصفحة الافتراضية

#### 🔒 إعدادات الأمان
- مدة انتهاء كلمة المرور
- عدد محاولات تسجيل الدخول
- تسجيل الأنشطة
- النسخ الاحتياطي التلقائي

#### 📊 إعدادات النظام
- مسار النسخ الاحتياطية
- الحد الأقصى لحجم الملفات
- فترة النسخ التلقائي
- تنظيف السجلات القديمة

## 🎨 نظام الألوان الاحترافي

| اللون | الاستخدام | مثال |
|--------|-----------|-------|
| 🔵 أزرق (#4F81BD) | الإجراءات الأساسية | أزرار الحفظ والتحديث |
| 🟢 أخضر (#28A745) | الإضافة والنجاح | إضافة جديد، رسائل النجاح |
| 🔴 أحمر (#DC3545) | الحذف والخطر | أزرار الحذف، رسائل الخطأ |
| 🟠 برتقالي (#FFC107) | التحذيرات | تنبيهات، رسائل التحذير |
| 🔷 أزرق فاتح (#17A2B8) | المعلومات | رسائل المعلومات |
| ⚫ رمادي (#6C757D) | الإجراءات الثانوية | أزرار الإلغاء |

## 🔧 الوظائف البرمجية المتقدمة

### وظائف الأمان
- `LoginUser()` - تسجيل الدخول الآمن
- `LogActivity()` - تسجيل جميع الأنشطة
- `CheckUserPermissions()` - فحص الصلاحيات

### وظائف إدارة البيانات
- `GenerateBookNumber()` - توليد أرقام الكتب تلقائياً
- `AdvancedSearch()` - البحث المتقدم
- `ExportToExcel()` - تصدير البيانات
- `ImportFromExcel()` - استيراد البيانات

### وظائف النسخ الاحتياطي
- `CreateBackup()` - إنشاء نسخة احتياطية
- `RestoreBackup()` - استعادة النسخة
- `ScheduleBackup()` - جدولة النسخ التلقائية

### وظائف التقارير
- `GenerateMonthlyReport()` - التقرير الشهري
- `GenerateDepartmentReport()` - تقرير الجهة
- `ExportReportToPDF()` - تصدير التقارير

## 📱 الاستخدام على الأجهزة المختلفة

### 💻 أجهزة الكمبيوتر المكتبية
- دقة الشاشة المثلى: 1920x1080 أو أعلى
- ذاكرة الوصول العشوائي: 4GB أو أكثر
- مساحة القرص الصلب: 500MB للنظام + مساحة للبيانات

### 💻 أجهزة الكمبيوتر المحمولة
- دقة الشاشة الدنيا: 1366x768
- يعمل بكفاءة على الشاشات الصغيرة
- دعم اللمس للأجهزة المناسبة

### 🖥️ الشبكات المحلية
- يمكن مشاركة قاعدة البيانات على الشبكة
- دعم متعدد المستخدمين
- تزامن البيانات التلقائي

## 🔒 الأمان والصلاحيات

### مستويات الأمان
1. **مستوى قاعدة البيانات**: حماية الملف بكلمة مرور
2. **مستوى المستخدم**: تسجيل دخول فردي
3. **مستوى الوظائف**: صلاحيات محددة لكل مستخدم
4. **مستوى البيانات**: تشفير البيانات الحساسة

### تسجيل الأنشطة
- جميع العمليات مسجلة مع التوقيت
- تتبع المستخدم المنفذ للعملية
- تفاصيل العملية المنفذة
- إمكانية مراجعة السجلات

## 📊 الإحصائيات والتحليلات

### الإحصائيات المتاحة
- إجمالي عدد الكتب
- الكتب المضافة حسب الفترة
- توزيع الكتب حسب الحالة
- أكثر الجهات نشاطاً
- إحصائيات المستخدمين

### التحليلات المتقدمة
- اتجاهات النمو الشهرية
- مقارنات سنوية
- تحليل الأداء
- توقعات مستقبلية

## 🚨 استكشاف الأخطاء وإصلاحها

### المشاكل الشائعة وحلولها

#### مشكلة: لا يمكن فتح قاعدة البيانات
**الحل**:
1. تأكد من وجود Microsoft Access
2. تحقق من صلاحيات الملف
3. تأكد من عدم فتح الملف في مكان آخر

#### مشكلة: خطأ في تسجيل الدخول
**الحل**:
1. تأكد من صحة اسم المستخدم وكلمة المرور
2. تحقق من تفعيل الحساب
3. اتصل بالمدير لإعادة تعيين كلمة المرور

#### مشكلة: بطء في الأداء
**الحل**:
1. قم بضغط وإصلاح قاعدة البيانات
2. احذف السجلات القديمة غير المطلوبة
3. أنشئ نسخة احتياطية وابدأ بقاعدة بيانات جديدة

#### مشكلة: فقدان البيانات
**الحل**:
1. استعد آخر نسخة احتياطية
2. تحقق من سجل الأنشطة
3. اتصل بالدعم الفني

### رسائل الخطأ الشائعة

| رسالة الخطأ | السبب | الحل |
|-------------|--------|------|
| "لا يمكن العثور على الملف" | مسار قاعدة البيانات غير صحيح | تحقق من مسار الملف |
| "الملف قيد الاستخدام" | فتح الملف في مكان آخر | أغلق جميع نسخ Access |
| "خطأ في الذاكرة" | نفاد ذاكرة النظام | أغلق البرامج الأخرى |
| "انتهت صلاحية كلمة المرور" | كلمة المرور قديمة | غير كلمة المرور |

## 📞 الدعم والمساعدة

### الحصول على المساعدة
1. **راجع هذا الدليل أولاً** - يحتوي على إجابات لمعظم الأسئلة
2. **تحقق من رسائل الخطأ** - غالباً ما تحتوي على معلومات مفيدة
3. **راجع سجل الأنشطة** - لفهم ما حدث قبل المشكلة
4. **اتصل بالدعم الفني** - للمساعدة المتخصصة

### معلومات مهمة للدعم الفني
عند طلب المساعدة، قدم المعلومات التالية:
- إصدار Microsoft Access
- نظام التشغيل
- وصف المشكلة بالتفصيل
- رسالة الخطأ (إن وجدت)
- الخطوات التي أدت للمشكلة

## 🔄 التحديثات والصيانة

### الصيانة الدورية
- **أسبوعياً**: إنشاء نسخة احتياطية
- **شهرياً**: ضغط وإصلاح قاعدة البيانات
- **ربع سنوياً**: مراجعة الصلاحيات والمستخدمين
- **سنوياً**: أرشفة البيانات القديمة

### التحديثات
- تحديثات الأمان
- إضافة مميزات جديدة
- تحسين الأداء
- إصلاح الأخطاء

## 🎓 نصائح للاستخدام الأمثل

### للمستخدمين الجدد
1. ابدأ بلوحة المعلومات لفهم النظام
2. جرب إضافة كتاب واحد أولاً
3. استخدم البحث البسيط قبل المتقدم
4. اطلب التدريب من المدير

### للمستخدمين المتقدمين
1. استخدم اختصارات لوحة المفاتيح
2. احفظ معايير البحث المفضلة
3. أنشئ تقارير مخصصة
4. استخدم النسخ الاحتياطي التلقائي

### للمديرين
1. راقب سجل الأنشطة بانتظام
2. راجع صلاحيات المستخدمين دورياً
3. أنشئ نسخ احتياطية متعددة
4. درب المستخدمين الجدد

## 📈 المميزات المستقبلية

### قيد التطوير
- 📱 تطبيق جوال مصاحب
- 🤖 ذكاء اصطناعي لتصنيف الكتب
- 📊 تقارير تفاعلية متقدمة
- 🔔 نظام إشعارات متطور
- ☁️ النسخ الاحتياطي السحابي

### طلبات المستخدمين
- تكامل مع البريد الإلكتروني
- دعم التوقيع الرقمي
- واجهة ويب للوصول عن بُعد
- تصدير لتنسيقات إضافية

---

## 🏆 الخلاصة

نظام إدارة الكتب المتكامل هو حل شامل ومتطور لإدارة الوثائق والكتب الرسمية. يوفر النظام جميع الأدوات اللازمة لإدارة فعالة مع واجهة مستخدم حديثة وسهلة الاستخدام.

**المميزات الرئيسية**:
- ✅ 9 أقسام متكاملة
- ✅ واجهة عربية احترافية
- ✅ نظام أمان متقدم
- ✅ تقارير شاملة
- ✅ نسخ احتياطي تلقائي
- ✅ بحث متقدم
- ✅ مسح ضوئي متطور

**للبدء**: افتح قاعدة البيانات، سجل الدخول، واستمتع بتجربة إدارة متطورة!

---

**تم تطوير هذا النظام بأحدث معايير Microsoft Access لضمان الأداء الأمثل والاستقرار العالي**

📧 للاستفسارات والدعم الفني، يرجى الاتصال بفريق التطوير