# إنشاء قاعدة بيانات Access
$dbPath = "c:\Users\<USER>\Desktop\ALI-ARSHFA - 2\BookManagementSystem.accdb"

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    $access.NewCurrentDatabase($dbPath)
    
    # إنشاء جدول الكتب
    $sql1 = "CREATE TABLE tbl_Books (BookID AUTOINCREMENT PRIMARY KEY, BookNumber TEXT(50), BookDate DATE, Subject TEXT(255), SentTo TEXT(100), ExecutionDept TEXT(100), Details MEMO, Status TEXT(50))"
    $access.CurrentDb.Execute($sql1)
    
    # إنشاء جدول المستخدمين
    $sql2 = "CREATE TABLE tbl_Users (UserID AUTOINCREMENT PRIMARY KEY, Username TEXT(50), Password TEXT(100), FullName TEXT(100), Role TEXT(20))"
    $access.CurrentDb.Execute($sql2)
    
    # إنشاء جدول الأقسام
    $sql3 = "CREATE TABLE tbl_Departments (DeptID AUTOINCREMENT PRIMARY KEY, DeptName TEXT(100), DeptType TEXT(20))"
    $access.CurrentDb.Execute($sql3)
    
    # إدراج مستخدم افتراضي
    $sql4 = "INSERT INTO tbl_Users (Username, Password, FullName, Role) VALUES ('admin', 'admin123', 'Manager', 'Admin')"
    $access.CurrentDb.Execute($sql4)
    
    # إدراج أقسام
    $sql5 = "INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('Management', 'Sender')"
    $access.CurrentDb.Execute($sql5)
    
    $sql6 = "INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('Maintenance', 'Executor')"
    $access.CurrentDb.Execute($sql6)
    
    # إدراج كتاب تجريبي
    $sql7 = "INSERT INTO tbl_Books (BookNumber, BookDate, Subject, SentTo, ExecutionDept, Status) VALUES ('2024/001', Date(), 'Test Book', 'Management', 'Maintenance', 'New')"
    $access.CurrentDb.Execute($sql7)
    
    $access.CloseCurrentDatabase()
    $access.Quit()
    
    Write-Host "Database created successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}