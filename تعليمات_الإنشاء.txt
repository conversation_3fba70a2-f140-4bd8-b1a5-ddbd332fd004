خطوات إنشاء قاعدة البيانات يدوياً:

1. افتح Microsoft Access
2. اختر "قاعدة بيانات فارغة"
3. اكتب الاسم: BookManagementSystem
4. اختر المجلد: c:\Users\<USER>\Desktop\ALI-ARSHFA - 2
5. انقر "إنشاء"

بعد إنشاء قاعدة البيانات، انسخ والصق الكود التالي في نافذة SQL:

-- إن<PERSON>اء جدول الكتب
CREATE TABLE tbl_Books (
    BookID AUTOINCREMENT PRIMARY KEY,
    BookNumber TEXT(50) NOT NULL,
    BookDate DATE NOT NULL,
    Subject TEXT(255) NOT NULL,
    SentTo TEXT(100) NOT NULL,
    ExecutionDept TEXT(100) NOT NULL,
    Details MEMO,
    Status TEXT(50) DEFAULT 'جديد',
    CreatedDate DATE DEFAULT Now(),
    IsArchived YESNO DEFAULT No
);

-- إ<PERSON><PERSON><PERSON><PERSON> جدول المستخدمين
CREATE TABLE tbl_Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(100) NOT NULL,
    FullName TEXT(100) NOT NULL,
    Role TEXT(20) DEFAULT 'مستخدم',
    IsActive YESNO DEFAULT Yes
);

-- إنشاء جدول الأقسام
CREATE TABLE tbl_Departments (
    DeptID AUTOINCREMENT PRIMARY KEY,
    DeptName TEXT(100) NOT NULL,
    DeptType TEXT(20),
    IsActive YESNO DEFAULT Yes
);

-- إنشاء جدول سجل الأنشطة
CREATE TABLE tbl_ActivityLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID INTEGER,
    Activity TEXT(255) NOT NULL,
    ActivityDate DATE DEFAULT Now(),
    Details MEMO
);

-- إدراج البيانات الأولية
INSERT INTO tbl_Users (Username, Password, FullName, Role) 
VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');

INSERT INTO tbl_Departments (DeptName, DeptType) VALUES
('الإدارة العليا', 'مرسل'),
('قسم الصيانة', 'منفذ'),
('الإدارة الفنية', 'منفذ'),
('قسم التقارير', 'منفذ');

-- إدراج بيانات تجريبية
INSERT INTO tbl_Books (BookNumber, BookDate, Subject, SentTo, ExecutionDept, Status) VALUES
('2024/001', Date(), 'طلب صيانة المعدات', 'قسم الصيانة', 'الإدارة الفنية', 'قيد التنفيذ'),
('2024/002', Date()-1, 'تقرير شهري', 'الإدارة العليا', 'قسم التقارير', 'مكتمل');

بعد تنفيذ الكود، ستكون قاعدة البيانات جاهزة للاستخدام.