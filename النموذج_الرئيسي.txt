كود النموذج الرئيسي - frm_Main

1. أنشئ نموذج جديد في Access
2. اسم النموذج: frm_Main
3. أضف العناصر التالية:

العناصر المطلوبة:
- Label: lbl_Title (النص: "نظام إدارة الكتب المتكامل")
- Label: lbl_CurrentUser
- Tab Control: TabCtl_Main مع 9 تبويبات
- Subform: subMain

كود النموذج:

Option Compare Database
Option Explicit

Public CurrentUserID As Integer
Public CurrentUserName As String
Public CurrentUserRole As String

Private Sub Form_Load()
    ' تسجيل دخول افتراضي للاختبار
    CurrentUserID = 1
    CurrentUserName = "مدير النظام"
    CurrentUserRole = "مدير"
    
    Me.lbl_CurrentUser.Caption = "مرحباً، " & CurrentUserName
    
    ' تحميل لوحة المعلومات
    LoadDashboard
End Sub

Private Sub TabCtl_Main_Change()
    Select Case Me.TabCtl_Main.Value
        Case 0: LoadDashboard
        Case 1: LoadBooks
        Case 2: LoadScanning
        Case 3: LoadSearch
        Case 4: LoadReports
        Case 5: LoadArchive
        Case 6: LoadUsers
        Case 7: LoadBackup
        Case 8: LoadSettings
    End Select
End Sub

Private Sub LoadDashboard()
    ' تحميل إحصائيات لوحة المعلومات
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    
    ' إجمالي الكتب
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS Total FROM tbl_Books")
    If Not rs.EOF Then
        Me.lbl_TotalBooks.Caption = rs!Total
    End If
    rs.Close
    
    ' كتب اليوم
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS Total FROM tbl_Books WHERE BookDate = Date()")
    If Not rs.EOF Then
        Me.lbl_TodayBooks.Caption = rs!Total
    End If
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
End Sub

Private Sub LoadBooks()
    ' فتح نموذج إدارة الكتب
    DoCmd.OpenForm "frm_Books", acNormal
End Sub

Private Sub btn_AddBook_Click()
    DoCmd.OpenForm "frm_BookEntry", acNormal
End Sub

Private Sub btn_Search_Click()
    DoCmd.OpenForm "frm_Search", acNormal
End Sub

Private Sub btn_Reports_Click()
    DoCmd.OpenForm "frm_Reports", acNormal
End Sub