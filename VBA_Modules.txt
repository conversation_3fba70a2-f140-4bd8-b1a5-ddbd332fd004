' ===== وحدة الوظائف العامة =====
' Module: mdl_General

Option Compare Database
Option Explicit

' متغيرات عامة
Public CurrentUserID As Integer
Public CurrentUserName As String
Public CurrentUserRole As String

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim rs As DAO.Recordset
    Dim db As DAO.Database
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM tbl_Users WHERE Username='" & Username & "' AND Password='" & Password & "' AND IsActive=True")
    
    If Not rs.EOF Then
        CurrentUserID = rs!UserID
        CurrentUserName = rs!FullName
        CurrentUserRole = rs!Role
        
        ' تحديث آخر تسجيل دخول
        db.Execute "UPDATE tbl_Users SET LastLogin=Now() WHERE UserID=" & CurrentUserID
        
        LoginUser = True
        LogActivity "تسجيل دخول", 0, "تم تسجيل الدخول بنجاح"
    Else
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة تسجيل الأنشطة
Public Sub LogActivity(Activity As String, BookID As Integer, Details As String)
    Dim db As DAO.Database
    Set db = CurrentDb
    
    db.Execute "INSERT INTO tbl_ActivityLog (UserID, Activity, BookID, Details) VALUES (" & _
               CurrentUserID & ", '" & Activity & "', " & IIf(BookID = 0, "NULL", BookID) & ", '" & Details & "')"
    
    Set db = Nothing
End Sub

' وظيفة إنشاء رقم كتاب تلقائي
Public Function GenerateBookNumber() As String
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim NextNumber As Integer
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT MAX(Val(Right(BookNumber, 3))) AS MaxNum FROM tbl_Books WHERE BookNumber LIKE '" & Year(Date) & "/%'")
    
    If IsNull(rs!MaxNum) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNum + 1
    End If
    
    GenerateBookNumber = Year(Date) & "/" & Format(NextNumber, "000")
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة النسخ الاحتياطي
Public Sub CreateBackup()
    Dim BackupPath As String
    Dim BackupName As String
    Dim db As DAO.Database
    
    BackupName = "Backup_" & Format(Date, "yyyy-mm-dd") & "_" & Format(Time, "hh-nn-ss") & ".accdb"
    BackupPath = CurrentProject.Path & "\Backups\" & BackupName
    
    ' إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    If Dir(CurrentProject.Path & "\Backups\", vbDirectory) = "" Then
        MkDir CurrentProject.Path & "\Backups\"
    End If
    
    ' نسخ قاعدة البيانات
    FileCopy CurrentProject.FullName, BackupPath
    
    ' تسجيل النسخة الاحتياطية في قاعدة البيانات
    Set db = CurrentDb
    db.Execute "INSERT INTO tbl_Backups (BackupName, BackupPath, BackupSize, CreatedBy) VALUES ('" & _
               BackupName & "', '" & BackupPath & "', " & FileLen(BackupPath) & ", '" & CurrentUserName & "')"
    
    Set db = Nothing
    
    MsgBox "تم إنشاء النسخة الاحتياطية بنجاح: " & BackupName, vbInformation
    LogActivity "نسخ احتياطي", 0, "تم إنشاء نسخة احتياطية: " & BackupName
End Sub

' وظيفة البحث المتقدم
Public Function AdvancedSearch(BookNumber As String, FromDate As Date, ToDate As Date, _
                              Subject As String, SentTo As String, ExecutionDept As String) As String
    Dim SQL As String
    Dim WhereClause As String
    
    SQL = "SELECT * FROM tbl_Books WHERE 1=1"
    
    If BookNumber <> "" Then
        WhereClause = WhereClause & " AND BookNumber LIKE '*" & BookNumber & "*'"
    End If
    
    If Not IsNull(FromDate) And FromDate <> #12:00:00 AM# Then
        WhereClause = WhereClause & " AND BookDate >= #" & FromDate & "#"
    End If
    
    If Not IsNull(ToDate) And ToDate <> #12:00:00 AM# Then
        WhereClause = WhereClause & " AND BookDate <= #" & ToDate & "#"
    End If
    
    If Subject <> "" Then
        WhereClause = WhereClause & " AND Subject LIKE '*" & Subject & "*'"
    End If
    
    If SentTo <> "" Then
        WhereClause = WhereClause & " AND SentTo LIKE '*" & SentTo & "*'"
    End If
    
    If ExecutionDept <> "" Then
        WhereClause = WhereClause & " AND ExecutionDept LIKE '*" & ExecutionDept & "*'"
    End If
    
    AdvancedSearch = SQL & WhereClause & " ORDER BY BookDate DESC"
End Function

' وظيفة تصدير البيانات
Public Sub ExportToExcel()
    Dim ExcelApp As Object
    Dim ExcelBook As Object
    Dim ExcelSheet As Object
    Dim rs As DAO.Recordset
    Dim db As DAO.Database
    Dim i As Integer
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM tbl_Books ORDER BY BookDate DESC")
    
    ' إنشاء تطبيق Excel
    Set ExcelApp = CreateObject("Excel.Application")
    Set ExcelBook = ExcelApp.Workbooks.Add
    Set ExcelSheet = ExcelBook.Worksheets(1)
    
    ' إضافة العناوين
    ExcelSheet.Cells(1, 1) = "رقم الكتاب"
    ExcelSheet.Cells(1, 2) = "التاريخ"
    ExcelSheet.Cells(1, 3) = "الموضوع"
    ExcelSheet.Cells(1, 4) = "الجهة المرسل إليها"
    ExcelSheet.Cells(1, 5) = "جهة التنفيذ"
    ExcelSheet.Cells(1, 6) = "التفاصيل"
    ExcelSheet.Cells(1, 7) = "الحالة"
    
    ' تنسيق العناوين
    With ExcelSheet.Range("A1:G1")
        .Font.Bold = True
        .Interior.Color = RGB(79, 129, 189)
        .Font.Color = RGB(255, 255, 255)
    End With
    
    ' إضافة البيانات
    i = 2
    Do While Not rs.EOF
        ExcelSheet.Cells(i, 1) = rs!BookNumber
        ExcelSheet.Cells(i, 2) = rs!BookDate
        ExcelSheet.Cells(i, 3) = rs!Subject
        ExcelSheet.Cells(i, 4) = rs!SentTo
        ExcelSheet.Cells(i, 5) = rs!ExecutionDept
        ExcelSheet.Cells(i, 6) = rs!Details
        ExcelSheet.Cells(i, 7) = rs!Status
        
        i = i + 1
        rs.MoveNext
    Loop
    
    ' تنسيق الجدول
    ExcelSheet.Columns.AutoFit
    
    ' حفظ الملف
    Dim SavePath As String
    SavePath = CurrentProject.Path & "\تصدير_الكتب_" & Format(Date, "yyyy-mm-dd") & ".xlsx"
    ExcelBook.SaveAs SavePath
    
    ExcelApp.Visible = True
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox "تم تصدير البيانات بنجاح إلى: " & SavePath, vbInformation
    LogActivity "تصدير بيانات", 0, "تم تصدير البيانات إلى Excel"
End Sub

' وظيفة إحصائيات لوحة المعلومات
Public Function GetDashboardStats() As String
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim TotalBooks As Integer
    Dim TodayBooks As Integer
    Dim ArchivedBooks As Integer
    Dim PendingBooks As Integer
    
    Set db = CurrentDb
    
    ' إجمالي الكتب
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS Total FROM tbl_Books")
    TotalBooks = rs!Total
    rs.Close
    
    ' كتب اليوم
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS Total FROM tbl_Books WHERE BookDate = Date()")
    TodayBooks = rs!Total
    rs.Close
    
    ' الكتب المؤرشفة
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS Total FROM tbl_Books WHERE IsArchived = True")
    ArchivedBooks = rs!Total
    rs.Close
    
    ' الكتب قيد التنفيذ
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS Total FROM tbl_Books WHERE Status = 'قيد التنفيذ'")
    PendingBooks = rs!Total
    rs.Close
    
    GetDashboardStats = TotalBooks & "|" & TodayBooks & "|" & ArchivedBooks & "|" & PendingBooks
    
    Set rs = Nothing
    Set db = Nothing
End Function

' ===== وحدة معالجة الصور =====
' Module: mdl_ImageProcessing

' وظيفة حفظ الصورة في قاعدة البيانات
Public Sub SaveImageToDatabase(BookID As Integer, ImagePath As String)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM tbl_Books WHERE BookID = " & BookID)
    
    If Not rs.EOF Then
        rs.Edit
        rs!BookImage.LoadFromFile ImagePath
        rs.Update
        
        LogActivity "حفظ صورة", BookID, "تم حفظ صورة للكتاب"
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' وظيفة استخراج الصورة من قاعدة البيانات
Public Sub ExtractImageFromDatabase(BookID As Integer, SavePath As String)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM tbl_Books WHERE BookID = " & BookID)
    
    If Not rs.EOF And Not IsNull(rs!BookImage) Then
        rs!BookImage.SaveToFile SavePath
        LogActivity "استخراج صورة", BookID, "تم استخراج صورة الكتاب"
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' ===== وحدة التقارير =====
' Module: mdl_Reports

' وظيفة إنشاء تقرير شهري
Public Sub GenerateMonthlyReport(ReportMonth As Integer, ReportYear As Integer)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    SQL = "SELECT * FROM tbl_Books WHERE Month(BookDate) = " & ReportMonth & _
          " AND Year(BookDate) = " & ReportYear & " ORDER BY BookDate"
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset(SQL)
    
    ' فتح تقرير أو إنشاء ملف Excel
    DoCmd.OpenReport "rpt_MonthlyReport", acViewPreview
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    LogActivity "تقرير شهري", 0, "تم إنشاء تقرير شهري لـ " & ReportMonth & "/" & ReportYear
End Sub

' وظيفة إنشاء تقرير حسب الجهة
Public Sub GenerateDepartmentReport(DeptName As String)
    Dim SQL As String
    
    SQL = "SELECT * FROM tbl_Books WHERE SentTo = '" & DeptName & "' OR ExecutionDept = '" & DeptName & "' ORDER BY BookDate DESC"
    
    ' تطبيق الفلتر على التقرير
    DoCmd.OpenReport "rpt_DepartmentReport", acViewPreview, , "SentTo = '" & DeptName & "' OR ExecutionDept = '" & DeptName & "'"
    
    LogActivity "تقرير جهة", 0, "تم إنشاء تقرير للجهة: " & DeptName
End Sub