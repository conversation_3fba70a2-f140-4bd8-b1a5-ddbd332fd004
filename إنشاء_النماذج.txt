خطوات إنشاء النماذج في Access:

1. النموذج الرئيسي:
   - Create → Form Design
   - اسم النموذج: frm_Main
   - أضف Label: "نظام إدارة الكتب المتكامل"
   - أضف أزرار:
     * btn_Books (إدارة الكتب)
     * btn_Search (البحث)
     * btn_Reports (التقارير)

2. نموذج الكتب:
   - Create → Form Wizard
   - اختر جدول: tbl_Books
   - اختر جميع الحقول
   - Layout: Columnar
   - اسم النموذج: frm_Books

3. نموذج البحث:
   - Create → Form Design
   - اسم النموذج: frm_Search
   - أضف حقول البحث:
     * txt_SearchNumber
     * txt_SearchSubject
     * btn_Search

كود الأزرار:

للنموذج الرئيسي - frm_Main:
Private Sub btn_Books_Click()
    DoCmd.OpenForm "frm_Books"
End Sub

Private Sub btn_Search_Click()
    DoCmd.OpenForm "frm_Search"
End Sub

لنموذج البحث - frm_Search:
Private Sub btn_Search_Click()
    Dim sql As String
    sql = "SELECT * FROM tbl_Books WHERE 1=1"
    
    If Not IsNull(Me.txt_SearchNumber) Then
        sql = sql & " AND BookNumber LIKE '*" & Me.txt_SearchNumber & "*'"
    End If
    
    If Not IsNull(Me.txt_SearchSubject) Then
        sql = sql & " AND Subject LIKE '*" & Me.txt_SearchSubject & "*'"
    End If
    
    DoCmd.OpenForm "frm_Books", , , , , , sql
End Sub