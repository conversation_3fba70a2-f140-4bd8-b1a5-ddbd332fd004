' ===== تصميم التقارير =====

' ===== التقرير الشهري - rpt_MonthlyReport =====
' Record Source: qry_MonthlyBooks
' Page Setup: A4, Portrait

' أقسام التقرير:
' 1. Report Header:
'    - Label: "تقرير الكتب الشهري"
'    - Label: الشهر والسنة
'    - Label: تاريخ الطباعة
'    - Logo: شعار المؤسسة

' 2. Page Header:
'    - عناوين الأعمدة: رقم الكتاب، التاريخ، الموضوع، الجهة المرسل إليها، جهة التنفيذ، الحالة

' 3. Detail:
'    - البيانات من الاستعلام

' 4. Report Footer:
'    - إجمالي عدد الكتب
'    - إحصائيات حسب الحالة
'    - توقيع المسؤول

' الاستعلام المرتبط - qry_MonthlyBooks:
SELECT tbl_Books.BookNumber, tbl_Books.BookDate, tbl_Books.Subject, 
       tbl_Books.SentTo, tbl_Books.ExecutionDept, tbl_Books.Status
FROM tbl_Books
WHERE Month([BookDate]) = [Forms]![frm_Reports]![cmb_Month] 
  AND Year([BookDate]) = [Forms]![frm_Reports]![cmb_Year]
ORDER BY tbl_Books.BookDate;

' ===== التقرير السنوي - rpt_YearlyReport =====
' Record Source: qry_YearlyBooks

' أقسام التقرير:
' 1. Report Header:
'    - Label: "التقرير السنوي للكتب"
'    - Label: السنة
'    - رسم بياني: توزيع الكتب حسب الأشهر

' 2. Group Header (حسب الشهر):
'    - اسم الشهر
'    - عدد كتب الشهر

' 3. Detail:
'    - تفاصيل الكتب

' 4. Group Footer:
'    - إجمالي كتب الشهر

' 5. Report Footer:
'    - الإجمالي العام
'    - إحصائيات شاملة

' الاستعلام المرتبط - qry_YearlyBooks:
SELECT tbl_Books.*, Month([BookDate]) AS BookMonth, 
       MonthName(Month([BookDate])) AS MonthName
FROM tbl_Books
WHERE Year([BookDate]) = [Forms]![frm_Reports]![cmb_Year]
ORDER BY tbl_Books.BookDate;

' ===== تقرير حسب الجهة - rpt_DepartmentReport =====
' Record Source: qry_DepartmentBooks

' أقسام التقرير:
' 1. Report Header:
'    - Label: "تقرير كتب الجهة"
'    - Label: اسم الجهة
'    - فترة التقرير

' 2. Group Header (حسب نوع الجهة):
'    - "الكتب المرسلة إلى الجهة"
'    - "الكتب المنفذة بواسطة الجهة"

' 3. Detail:
'    - تفاصيل الكتب

' 4. Group Footer:
'    - عدد الكتب في كل مجموعة

' الاستعلام المرتبط - qry_DepartmentBooks:
SELECT tbl_Books.*, 
       IIf([SentTo]=[Forms]![frm_Reports]![cmb_Department],"مرسل إليها","منفذة بواسطة") AS RelationType
FROM tbl_Books
WHERE tbl_Books.SentTo = [Forms]![frm_Reports]![cmb_Department] 
   OR tbl_Books.ExecutionDept = [Forms]![frm_Reports]![cmb_Department]
ORDER BY RelationType, tbl_Books.BookDate;

' ===== تقرير الأنشطة - rpt_ActivityReport =====
' Record Source: qry_ActivityLog

' أقسام التقرير:
' 1. Report Header:
'    - Label: "تقرير سجل الأنشطة"
'    - فترة التقرير

' 2. Group Header (حسب المستخدم):
'    - اسم المستخدم
'    - عدد أنشطته

' 3. Detail:
'    - تاريخ ووقت النشاط
'    - نوع النشاط
'    - التفاصيل

' الاستعلام المرتبط - qry_ActivityLog:
SELECT tbl_ActivityLog.*, tbl_Users.FullName
FROM tbl_ActivityLog 
INNER JOIN tbl_Users ON tbl_ActivityLog.UserID = tbl_Users.UserID
WHERE tbl_ActivityLog.ActivityDate >= Date()-30
ORDER BY tbl_Users.FullName, tbl_ActivityLog.ActivityDate DESC;

' ===== تقرير الإحصائيات - rpt_Statistics =====
' Record Source: qry_Statistics

' محتوى التقرير:
' 1. إحصائيات عامة:
'    - إجمالي الكتب
'    - الكتب هذا الشهر
'    - الكتب المؤرشفة
'    - الكتب قيد التنفيذ

' 2. رسوم بيانية:
'    - توزيع الكتب حسب الحالة
'    - توزيع الكتب حسب الجهات
'    - اتجاه الكتب عبر الأشهر

' 3. جداول تفصيلية:
'    - أكثر الجهات إرسالاً
'    - أكثر الجهات تنفيذاً
'    - المستخدمين الأكثر نشاطاً

' الاستعلامات المرتبطة:
' qry_BooksByStatus:
SELECT Status, COUNT(*) AS BookCount
FROM tbl_Books
GROUP BY Status;

' qry_BooksByDepartment:
SELECT SentTo AS Department, COUNT(*) AS BookCount, "مرسل" AS Type
FROM tbl_Books
GROUP BY SentTo
UNION ALL
SELECT ExecutionDept AS Department, COUNT(*) AS BookCount, "منفذ" AS Type
FROM tbl_Books
GROUP BY ExecutionDept;

' qry_BooksByMonth:
SELECT Format([BookDate],"yyyy-mm") AS YearMonth, 
       COUNT(*) AS BookCount
FROM tbl_Books
WHERE BookDate >= DateAdd("m",-12,Date())
GROUP BY Format([BookDate],"yyyy-mm")
ORDER BY Format([BookDate],"yyyy-mm");

' ===== تقرير النسخ الاحتياطية - rpt_BackupReport =====
' Record Source: tbl_Backups

' محتوى التقرير:
' - قائمة جميع النسخ الاحتياطية
' - تاريخ إنشاء كل نسخة
' - حجم النسخة
' - المستخدم الذي أنشأها
' - حالة النسخة (متاحة/تالفة)

' ===== كود VBA للتقارير =====

' كود التقرير الشهري:
Private Sub Report_Open(Cancel As Integer)
    Dim ReportTitle As String
    ReportTitle = "تقرير الكتب لشهر " & MonthName([Forms]![frm_Reports]![cmb_Month]) & _
                  " " & [Forms]![frm_Reports]![cmb_Year]
    Me.lbl_ReportTitle.Caption = ReportTitle
    Me.lbl_PrintDate.Caption = "تاريخ الطباعة: " & Format(Now, "dd/mm/yyyy hh:nn")
End Sub

Private Sub Report_Close()
    LogActivity "طباعة تقرير", 0, "تم طباعة التقرير الشهري"
End Sub

' كود حساب الإحصائيات في التقرير:
Private Sub ReportFooter_Format(Cancel As Integer, FormatCount As Integer)
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    Set db = CurrentDb
    
    ' حساب إجمالي الكتب
    SQL = "SELECT COUNT(*) AS Total FROM tbl_Books WHERE " & _
          "Month(BookDate) = " & [Forms]![frm_Reports]![cmb_Month] & _
          " AND Year(BookDate) = " & [Forms]![frm_Reports]![cmb_Year]
    Set rs = db.OpenRecordset(SQL)
    Me.lbl_TotalBooks.Caption = "إجمالي الكتب: " & rs!Total
    rs.Close
    
    ' حساب الكتب المكتملة
    SQL = "SELECT COUNT(*) AS Total FROM tbl_Books WHERE Status = 'مكتمل' AND " & _
          "Month(BookDate) = " & [Forms]![frm_Reports]![cmb_Month] & _
          " AND Year(BookDate) = " & [Forms]![frm_Reports]![cmb_Year]
    Set rs = db.OpenRecordset(SQL)
    Me.lbl_CompletedBooks.Caption = "الكتب المكتملة: " & rs!Total
    rs.Close
    
    ' حساب الكتب قيد التنفيذ
    SQL = "SELECT COUNT(*) AS Total FROM tbl_Books WHERE Status = 'قيد التنفيذ' AND " & _
          "Month(BookDate) = " & [Forms]![frm_Reports]![cmb_Month] & _
          " AND Year(BookDate) = " & [Forms]![frm_Reports]![cmb_Year]
    Set rs = db.OpenRecordset(SQL)
    Me.lbl_PendingBooks.Caption = "قيد التنفيذ: " & rs!Total
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' ===== تنسيق التقارير =====

' الألوان المستخدمة:
' - Header Background: RGB(79, 129, 189) - أزرق
' - Header Text: RGB(255, 255, 255) - أبيض
' - Alternate Row: RGB(242, 242, 242) - رمادي فاتح
' - Border: RGB(166, 166, 166) - رمادي متوسط

' الخطوط:
' - العناوين: Arial, 14pt, Bold
' - النصوص: Arial, 10pt, Regular
' - الأرقام: Arial, 10pt, Bold

' التخطيط:
' - Margins: 2cm من جميع الجهات
' - Line Spacing: 1.2
' - Column Width: متناسب مع المحتوى

' ===== طباعة التقارير =====

' كود طباعة مخصص:
Public Sub PrintReportWithOptions(ReportName As String)
    ' إعدادات الطباعة
    DoCmd.OpenReport ReportName, acViewPreview
    
    ' خيارات الطباعة
    With Application.Printer
        .PaperSize = acPRPSA4
        .Orientation = acPRORPortrait
        .TopMargin = 720  ' 2cm
        .BottomMargin = 720
        .LeftMargin = 720
        .RightMargin = 720
    End With
    
    ' طباعة أو معاينة
    If MsgBox("هل تريد طباعة التقرير مباشرة؟", vbYesNo + vbQuestion) = vbYes Then
        DoCmd.PrintOut acPrintAll
        DoCmd.Close acReport, ReportName
    End If
End Sub

' ===== تصدير التقارير =====

' تصدير إلى PDF:
Public Sub ExportReportToPDF(ReportName As String)
    Dim PDFPath As String
    PDFPath = CurrentProject.Path & "\" & ReportName & "_" & Format(Date, "yyyy-mm-dd") & ".pdf"
    
    DoCmd.OutputTo acOutputReport, ReportName, acFormatPDF, PDFPath
    
    MsgBox "تم تصدير التقرير إلى: " & PDFPath, vbInformation
    LogActivity "تصدير تقرير", 0, "تم تصدير التقرير: " & ReportName
End Sub

' تصدير إلى Excel:
Public Sub ExportReportToExcel(ReportName As String)
    Dim ExcelPath As String
    ExcelPath = CurrentProject.Path & "\" & ReportName & "_" & Format(Date, "yyyy-mm-dd") & ".xlsx"
    
    DoCmd.OutputTo acOutputReport, ReportName, acFormatXLSX, ExcelPath
    
    MsgBox "تم تصدير التقرير إلى: " & ExcelPath, vbInformation
    LogActivity "تصدير تقرير", 0, "تم تصدير التقرير إلى Excel: " & ReportName
End Sub