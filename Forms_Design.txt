' ===== تصميم النماذج =====

' ===== النموذج الرئيسي - frm_Main =====
' خصائص النموذج:
' - Navigation Buttons: No
' - Record Selectors: No
' - Dividing Lines: No
' - Auto Center: Yes
' - Border Style: Dialog
' - Control Box: Yes
' - Min Max Buttons: None
' - Close Button: Yes
' - Width: 1200 twips
' - Height: 800 twips

' العناصر:
' 1. شريط علوي (Header)
'    - Label: "نظام إدارة الكتب المتكامل" (Font: Arial, Size: 18, Bold: True, Color: Blue)
'    - Label: اسم المستخدم الحالي (Right aligned)
'    - Button: تسجيل الخروج

' 2. شريط جانبي (Sidebar) - Tab Control
'    - Tab 1: لوحة المعلومات
'    - Tab 2: إدارة الكتب  
'    - Tab 3: المسح الضوئي
'    - Tab 4: البحث المتقدم
'    - Tab 5: التقارير
'    - Tab 6: الأرشيف
'    - Tab 7: المستخدمين
'    - Tab 8: النسخ الاحتياطي
'    - Tab 9: الإعدادات

' 3. المنطقة الرئيسية - Subform Control
'    - Source Object: يتغير حسب التبويب المحدد

' كود النموذج الرئيسي:
Private Sub Form_Load()
    ' تحديد المستخدم الحالي
    Me.lbl_CurrentUser.Caption = "مرحباً، " & CurrentUserName
    
    ' تحميل لوحة المعلومات افتراضياً
    Me.subMain.SourceObject = "frm_Dashboard"
    
    ' تسجيل النشاط
    LogActivity "فتح النظام", 0, "تم فتح النظام الرئيسي"
End Sub

Private Sub TabCtl_Main_Change()
    Select Case Me.TabCtl_Main.Value
        Case 0: Me.subMain.SourceObject = "frm_Dashboard"
        Case 1: Me.subMain.SourceObject = "frm_Books"
        Case 2: Me.subMain.SourceObject = "frm_Scanning"
        Case 3: Me.subMain.SourceObject = "frm_Search"
        Case 4: Me.subMain.SourceObject = "frm_Reports"
        Case 5: Me.subMain.SourceObject = "frm_Archive"
        Case 6: Me.subMain.SourceObject = "frm_Users"
        Case 7: Me.subMain.SourceObject = "frm_Backup"
        Case 8: Me.subMain.SourceObject = "frm_Settings"
    End Select
End Sub

' ===== نموذج تسجيل الدخول - frm_Login =====
' خصائص النموذج:
' - Modal: Yes
' - Popup: Yes
' - Border Style: Dialog
' - Control Box: No
' - Width: 400 twips
' - Height: 300 twips

' العناصر:
' - Label: "تسجيل الدخول" (Title)
' - TextBox: اسم المستخدم (txt_Username)
' - TextBox: كلمة المرور (txt_Password, Input Mask: Password)
' - Button: دخول (btn_Login)
' - Button: إلغاء (btn_Cancel)

Private Sub btn_Login_Click()
    If LoginUser(Me.txt_Username.Value, Me.txt_Password.Value) Then
        DoCmd.Close acForm, Me.Name
        DoCmd.OpenForm "frm_Main"
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbCritical, "خطأ في تسجيل الدخول"
        Me.txt_Username.SetFocus
    End If
End Sub

' ===== نموذج لوحة المعلومات - frm_Dashboard =====
' العناصر:
' - 4 Labels للإحصائيات الرئيسية
' - Subform: الأنشطة الأخيرة
' - Chart: رسم بياني للإحصائيات

Private Sub Form_Load()
    Dim Stats As String
    Dim StatsArray() As String
    
    Stats = GetDashboardStats()
    StatsArray = Split(Stats, "|")
    
    Me.lbl_TotalBooks.Caption = StatsArray(0)
    Me.lbl_TodayBooks.Caption = StatsArray(1)
    Me.lbl_ArchivedBooks.Caption = StatsArray(2)
    Me.lbl_PendingBooks.Caption = StatsArray(3)
    
    ' تحديث الرسم البياني
    Me.Chart_Stats.Requery
End Sub

' ===== نموذج إدارة الكتب - frm_Books =====
' العناصر:
' - Continuous Forms View
' - Navigation Buttons: Yes
' - Record Source: tbl_Books
' - Filter: IsArchived = False

' حقول الإدخال:
' - txt_BookNumber (مع زر توليد تلقائي)
' - txt_BookDate (Date Picker)
' - txt_Subject
' - cmb_SentTo (Combo Box - مصدر البيانات: tbl_Departments)
' - cmb_ExecutionDept (Combo Box)
' - txt_Details (Memo)
' - img_BookImage (Image Control)
' - cmb_Status (Combo Box: جديد، قيد التنفيذ، مكتمل، مؤجل)

' أزرار الإجراءات:
' - btn_New: كتاب جديد
' - btn_Save: حفظ
' - btn_Delete: حذف
' - btn_AddImage: إضافة صورة
' - btn_Print: طباعة
' - btn_Archive: أرشفة

Private Sub btn_New_Click()
    DoCmd.GoToRecord , , acNewRec
    Me.txt_BookNumber.Value = GenerateBookNumber()
    Me.txt_BookDate.Value = Date
    Me.txt_BookNumber.SetFocus
End Sub

Private Sub btn_Save_Click()
    If Me.Dirty Then
        DoCmd.RunCommand acCmdSaveRecord
        LogActivity "حفظ كتاب", Me.BookID, "تم حفظ الكتاب: " & Me.txt_BookNumber.Value
        MsgBox "تم حفظ البيانات بنجاح", vbInformation
    End If
End Sub

Private Sub btn_Delete_Click()
    If MsgBox("هل أنت متأكد من حذف هذا الكتاب؟", vbYesNo + vbQuestion) = vbYes Then
        LogActivity "حذف كتاب", Me.BookID, "تم حذف الكتاب: " & Me.txt_BookNumber.Value
        DoCmd.RunCommand acCmdDeleteRecord
    End If
End Sub

Private Sub btn_AddImage_Click()
    Dim ImagePath As String
    ImagePath = Application.FileDialog(msoFileDialogFilePicker).Show
    
    If ImagePath <> "" Then
        SaveImageToDatabase Me.BookID, ImagePath
        Me.img_BookImage.Picture = ImagePath
        Me.Requery
    End If
End Sub

' ===== نموذج المسح الضوئي - frm_Scanning =====
' العناصر:
' - Image Control: منطقة عرض الصورة
' - Button: اختيار صورة
' - Button: مسح ضوئي (إذا كان متاح ماسح)
' - Button: حفظ كـ PDF
' - ListBox: قائمة الصور المحددة
' - ProgressBar: شريط التقدم

Private Sub btn_SelectImage_Click()
    Dim fd As FileDialog
    Set fd = Application.FileDialog(msoFileDialogFilePicker)
    
    With fd
        .Title = "اختيار صور للمسح"
        .AllowMultiSelect = True
        .Filters.Clear
        .Filters.Add "صور", "*.jpg;*.jpeg;*.png;*.bmp;*.gif"
        
        If .Show = -1 Then
            Dim i As Integer
            For i = 1 To .SelectedItems.Count
                Me.lst_Images.AddItem .SelectedItems(i)
            Next i
        End If
    End With
End Sub

Private Sub btn_SaveAsPDF_Click()
    ' كود تحويل الصور إلى PDF
    Dim PDFPath As String
    PDFPath = CurrentProject.Path & "\المسح_الضوئي_" & Format(Now, "yyyy-mm-dd_hh-nn-ss") & ".pdf"
    
    ' استخدام مكتبة خارجية لإنشاء PDF
    MsgBox "تم حفظ الملف كـ PDF: " & PDFPath, vbInformation
    LogActivity "حفظ PDF", 0, "تم إنشاء ملف PDF من المسح الضوئي"
End Sub

' ===== نموذج البحث المتقدم - frm_Search =====
' العناصر:
' - معايير البحث (TextBoxes و ComboBoxes)
' - Button: بحث
' - Button: مسح المعايير
' - Subform: نتائج البحث
' - Button: تصدير النتائج

Private Sub btn_Search_Click()
    Dim SQL As String
    SQL = AdvancedSearch(Me.txt_SearchBookNumber, Me.txt_FromDate, Me.txt_ToDate, _
                        Me.txt_SearchSubject, Me.cmb_SearchSentTo, Me.cmb_SearchExecutionDept)
    
    Me.subSearchResults.Form.RecordSource = SQL
    Me.subSearchResults.Requery
    
    LogActivity "بحث متقدم", 0, "تم تنفيذ بحث متقدم"
End Sub

Private Sub btn_ClearSearch_Click()
    Me.txt_SearchBookNumber = ""
    Me.txt_FromDate = Null
    Me.txt_ToDate = Null
    Me.txt_SearchSubject = ""
    Me.cmb_SearchSentTo = ""
    Me.cmb_SearchExecutionDept = ""
    
    Me.subSearchResults.Form.RecordSource = ""
    Me.subSearchResults.Requery
End Sub

' ===== نموذج التقارير - frm_Reports =====
' العناصر:
' - Option Group: نوع التقرير
' - ComboBox: الشهر (للتقرير الشهري)
' - ComboBox: السنة
' - ComboBox: الجهة (لتقرير الجهة)
' - Button: إنشاء التقرير
' - Button: معاينة
' - Button: طباعة

Private Sub btn_GenerateReport_Click()
    Select Case Me.opt_ReportType.Value
        Case 1: ' تقرير شهري
            GenerateMonthlyReport Me.cmb_Month.Value, Me.cmb_Year.Value
        Case 2: ' تقرير سنوي
            DoCmd.OpenReport "rpt_YearlyReport", acViewPreview, , "Year(BookDate) = " & Me.cmb_Year.Value
        Case 3: ' تقرير حسب الجهة
            GenerateDepartmentReport Me.cmb_Department.Value
        Case 4: ' تقرير الأنشطة
            DoCmd.OpenReport "rpt_ActivityReport", acViewPreview
    End Select
    
    LogActivity "إنشاء تقرير", 0, "تم إنشاء تقرير نوع: " & Me.opt_ReportType.Value
End Sub

' ===== نموذج النسخ الاحتياطي - frm_Backup =====
' العناصر:
' - Button: إنشاء نسخة احتياطية
' - Button: استعادة نسخة احتياطية
' - ListBox: قائمة النسخ الاحتياطية المتاحة
' - CheckBox: النسخ التلقائي
' - TextBox: فترة النسخ (بالأيام)

Private Sub btn_CreateBackup_Click()
    CreateBackup
    Me.lst_Backups.Requery
End Sub

Private Sub btn_RestoreBackup_Click()
    If Me.lst_Backups.ListIndex >= 0 Then
        Dim BackupPath As String
        BackupPath = Me.lst_Backups.Column(2) ' مسار النسخة الاحتياطية
        
        If MsgBox("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟" & vbCrLf & _
                  "سيتم استبدال البيانات الحالية!", vbYesNo + vbCritical) = vbYes Then
            
            ' كود استعادة النسخة الاحتياطية
            MsgBox "تم استعادة النسخة الاحتياطية بنجاح", vbInformation
            LogActivity "استعادة نسخة احتياطية", 0, "تم استعادة النسخة: " & Me.lst_Backups.Column(1)
        End If
    End If
End Sub

' ===== نموذج إدارة المستخدمين - frm_Users =====
' (للمدير فقط)
' العناصر:
' - Continuous Forms
' - Record Source: tbl_Users
' - حقول: Username, FullName, Email, Role, IsActive
' - أزرار: جديد، حفظ، حذف، تغيير كلمة المرور

Private Sub Form_Load()
    ' التحقق من صلاحيات المستخدم
    If CurrentUserRole <> "مدير" Then
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbCritical
        DoCmd.Close acForm, Me.Name
    End If
End Sub

' ===== نموذج الإعدادات - frm_Settings =====
' العناصر:
' - Record Source: tbl_Settings
' - حقول قابلة للتعديل لجميع الإعدادات
' - Button: حفظ الإعدادات
' - Button: استعادة الافتراضية

Private Sub btn_SaveSettings_Click()
    If Me.Dirty Then
        DoCmd.RunCommand acCmdSaveRecord
        MsgBox "تم حفظ الإعدادات بنجاح", vbInformation
        LogActivity "تحديث إعدادات", 0, "تم تحديث إعدادات النظام"
    End If
End Sub