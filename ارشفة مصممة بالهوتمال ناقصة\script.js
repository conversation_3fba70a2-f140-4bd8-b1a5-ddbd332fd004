// بيانات النظام
let booksData = [
    {
        id: 1,
        bookNumber: "2024/001",
        date: "2024-01-15",
        subject: "طلب صيانة المعدات",
        sentTo: "قسم الصيانة",
        executionDept: "الإدارة الفنية",
        details: "طلب صيانة عاجلة للمعدات",
        image: null,
        status: "قيد التنفيذ"
    },
    {
        id: 2,
        bookNumber: "2024/002", 
        date: "2024-01-14",
        subject: "تقرير شهري",
        sentTo: "الإدارة العليا",
        executionDept: "قسم التقارير",
        details: "تقرير الأنشطة الشهرية",
        image: null,
        status: "مكتمل"
    }
];

let currentUser = {
    name: "المدير",
    role: "admin",
    notifications: 3
};

// وظائف التنقل والواجهة
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    sidebar.classList.toggle('collapsed');
    mainContent.classList.toggle('expanded');
}

function showSection(sectionId) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));
    
    // إظهار القسم المحدد
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // تحديث القائمة الجانبية
    const menuItems = document.querySelectorAll('.sidebar-menu a');
    menuItems.forEach(item => item.classList.remove('active'));
    
    const activeItem = document.querySelector(`[onclick="showSection('${sectionId}')"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
    
    // تحميل بيانات القسم
    loadSectionData(sectionId);
}

function loadSectionData(sectionId) {
    switch(sectionId) {
        case 'books':
            loadBooksTable();
            break;
        case 'dashboard':
            updateDashboardStats();
            break;
    }
}

// وظائف إدارة الكتب
function loadBooksTable() {
    const tbody = document.getElementById('booksTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    booksData.forEach(book => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${book.bookNumber}</td>
            <td>${book.date}</td>
            <td>${book.subject}</td>
            <td>${book.sentTo}</td>
            <td>${book.executionDept}</td>
            <td>
                <button class="btn btn-primary" onclick="editBook(${book.id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-success" onclick="viewBook(${book.id})" title="عرض">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-danger" onclick="deleteBook(${book.id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function showAddBookModal() {
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalFooter = document.getElementById('modalFooter');
    
    modalTitle.textContent = 'إضافة كتاب جديد';
    
    modalBody.innerHTML = `
        <form id="addBookForm">
            <div class="form-group">
                <label>رقم الكتاب</label>
                <input type="text" class="form-control" id="bookNumber" required>
            </div>
            <div class="form-group">
                <label>تاريخ الكتاب</label>
                <input type="date" class="form-control" id="bookDate" required>
            </div>
            <div class="form-group">
                <label>الموضوع</label>
                <input type="text" class="form-control" id="bookSubject" required>
            </div>
            <div class="form-group">
                <label>الجهة المرسل إليها</label>
                <input type="text" class="form-control" id="sentTo" required>
            </div>
            <div class="form-group">
                <label>جهة التنفيذ</label>
                <input type="text" class="form-control" id="executionDept" required>
            </div>
            <div class="form-group">
                <label>التفاصيل</label>
                <textarea class="form-control" id="bookDetails" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label>صورة الكتاب</label>
                <input type="file" class="form-control" id="bookImage" accept="image/*">
            </div>
        </form>
    `;
    
    modalFooter.innerHTML = `
        <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        <button class="btn btn-primary" onclick="saveBook()">حفظ</button>
    `;
    
    showModal();
}

function saveBook() {
    const form = document.getElementById('addBookForm');
    const formData = new FormData(form);
    
    const newBook = {
        id: booksData.length + 1,
        bookNumber: document.getElementById('bookNumber').value,
        date: document.getElementById('bookDate').value,
        subject: document.getElementById('bookSubject').value,
        sentTo: document.getElementById('sentTo').value,
        executionDept: document.getElementById('executionDept').value,
        details: document.getElementById('bookDetails').value,
        image: null,
        status: "جديد"
    };
    
    if (validateBookData(newBook)) {
        booksData.push(newBook);
        loadBooksTable();
        closeModal();
        showAlert('تم إضافة الكتاب بنجاح', 'success');
        updateDashboardStats();
    }
}

function validateBookData(book) {
    if (!book.bookNumber || !book.date || !book.subject) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return false;
    }
    return true;
}

function editBook(bookId) {
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;
    
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalFooter = document.getElementById('modalFooter');
    
    modalTitle.textContent = 'تعديل الكتاب';
    
    modalBody.innerHTML = `
        <form id="editBookForm">
            <div class="form-group">
                <label>رقم الكتاب</label>
                <input type="text" class="form-control" id="editBookNumber" value="${book.bookNumber}" required>
            </div>
            <div class="form-group">
                <label>تاريخ الكتاب</label>
                <input type="date" class="form-control" id="editBookDate" value="${book.date}" required>
            </div>
            <div class="form-group">
                <label>الموضوع</label>
                <input type="text" class="form-control" id="editBookSubject" value="${book.subject}" required>
            </div>
            <div class="form-group">
                <label>الجهة المرسل إليها</label>
                <input type="text" class="form-control" id="editSentTo" value="${book.sentTo}" required>
            </div>
            <div class="form-group">
                <label>جهة التنفيذ</label>
                <input type="text" class="form-control" id="editExecutionDept" value="${book.executionDept}" required>
            </div>
            <div class="form-group">
                <label>التفاصيل</label>
                <textarea class="form-control" id="editBookDetails" rows="3">${book.details}</textarea>
            </div>
        </form>
    `;
    
    modalFooter.innerHTML = `
        <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
        <button class="btn btn-primary" onclick="updateBook(${bookId})">تحديث</button>
    `;
    
    showModal();
}

function updateBook(bookId) {
    const bookIndex = booksData.findIndex(b => b.id === bookId);
    if (bookIndex === -1) return;
    
    booksData[bookIndex] = {
        ...booksData[bookIndex],
        bookNumber: document.getElementById('editBookNumber').value,
        date: document.getElementById('editBookDate').value,
        subject: document.getElementById('editBookSubject').value,
        sentTo: document.getElementById('editSentTo').value,
        executionDept: document.getElementById('editExecutionDept').value,
        details: document.getElementById('editBookDetails').value
    };
    
    loadBooksTable();
    closeModal();
    showAlert('تم تحديث الكتاب بنجاح', 'success');
}

function viewBook(bookId) {
    const book = booksData.find(b => b.id === bookId);
    if (!book) return;
    
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalFooter = document.getElementById('modalFooter');
    
    modalTitle.textContent = 'عرض تفاصيل الكتاب';
    
    modalBody.innerHTML = `
        <div class="book-details">
            <div class="detail-row">
                <strong>رقم الكتاب:</strong> ${book.bookNumber}
            </div>
            <div class="detail-row">
                <strong>التاريخ:</strong> ${book.date}
            </div>
            <div class="detail-row">
                <strong>الموضوع:</strong> ${book.subject}
            </div>
            <div class="detail-row">
                <strong>الجهة المرسل إليها:</strong> ${book.sentTo}
            </div>
            <div class="detail-row">
                <strong>جهة التنفيذ:</strong> ${book.executionDept}
            </div>
            <div class="detail-row">
                <strong>التفاصيل:</strong> ${book.details}
            </div>
            <div class="detail-row">
                <strong>الحالة:</strong> <span class="status ${book.status === 'مكتمل' ? 'success' : 'pending'}">${book.status}</span>
            </div>
        </div>
    `;
    
    modalFooter.innerHTML = `
        <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
        <button class="btn btn-primary" onclick="editBook(${bookId})">تعديل</button>
    `;
    
    showModal();
}

function deleteBook(bookId) {
    if (confirm('هل أنت متأكد من حذف هذا الكتاب؟')) {
        booksData = booksData.filter(b => b.id !== bookId);
        loadBooksTable();
        showAlert('تم حذف الكتاب بنجاح', 'success');
        updateDashboardStats();
    }
}

// وظائف المسح الضوئي
function showScanModal() {
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalFooter = document.getElementById('modalFooter');
    
    modalTitle.textContent = 'المسح الضوئي للكتب';
    
    modalBody.innerHTML = `
        <div class="scan-container">
            <div class="scan-area" id="scanArea">
                <i class="fas fa-camera" style="font-size: 48px; color: #ccc;"></i>
                <p>اسحب الصورة هنا أو انقر للاختيار</p>
                <input type="file" id="scanInput" accept="image/*" multiple style="display: none;">
            </div>
            <div class="scan-options">
                <button class="btn btn-primary" onclick="startScan()">
                    <i class="fas fa-camera"></i> بدء المسح
                </button>
                <button class="btn btn-secondary" onclick="selectMultipleFiles()">
                    <i class="fas fa-images"></i> مسح متعدد
                </button>
                <button class="btn btn-success" onclick="generatePDF()">
                    <i class="fas fa-file-pdf"></i> إنشاء PDF
                </button>
            </div>
            <div class="scanned-images" id="scannedImages"></div>
        </div>
    `;
    
    modalFooter.innerHTML = `
        <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
        <button class="btn btn-primary" onclick="processScan()">معالجة المسح</button>
    `;
    
    setupScanArea();
    showModal();
}

function setupScanArea() {
    const scanArea = document.getElementById('scanArea');
    const scanInput = document.getElementById('scanInput');
    
    scanArea.addEventListener('click', () => scanInput.click());
    
    scanArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        scanArea.style.background = 'rgba(102, 126, 234, 0.1)';
    });
    
    scanArea.addEventListener('dragleave', () => {
        scanArea.style.background = '';
    });
    
    scanArea.addEventListener('drop', (e) => {
        e.preventDefault();
        scanArea.style.background = '';
        handleFiles(e.dataTransfer.files);
    });
    
    scanInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });
}

function handleFiles(files) {
    const scannedImages = document.getElementById('scannedImages');
    scannedImages.innerHTML = '';
    
    Array.from(files).forEach((file, index) => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageDiv = document.createElement('div');
                imageDiv.className = 'scanned-image';
                imageDiv.innerHTML = `
                    <img src="${e.target.result}" alt="صورة ممسوحة ${index + 1}">
                    <div class="image-actions">
                        <button class="btn btn-danger" onclick="removeImage(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                scannedImages.appendChild(imageDiv);
            };
            reader.readAsDataURL(file);
        }
    });
    
    showAlert(`تم تحميل ${files.length} صورة بنجاح`, 'success');
}

function startScan() {
    showProgressBar();
    updateProgress(0);
    
    // محاكاة عملية المسح
    let progress = 0;
    const interval = setInterval(() => {
        progress += 10;
        updateProgress(progress);
        
        if (progress >= 100) {
            clearInterval(interval);
            hideProgressBar();
            showAlert('تم المسح بنجاح', 'success');
        }
    }, 200);
}

function selectMultipleFiles() {
    const scanInput = document.getElementById('scanInput');
    scanInput.multiple = true;
    scanInput.click();
}

function generatePDF() {
    showAlert('جاري إنشاء ملف PDF...', 'info');
    
    setTimeout(() => {
        showAlert('تم إنشاء ملف PDF بنجاح', 'success');
    }, 2000);
}

function processScan() {
    const images = document.querySelectorAll('.scanned-image img');
    if (images.length === 0) {
        showAlert('لا توجد صور للمعالجة', 'warning');
        return;
    }
    
    showProgressBar();
    updateProgress(0);
    
    let progress = 0;
    const interval = setInterval(() => {
        progress += 20;
        updateProgress(progress);
        
        if (progress >= 100) {
            clearInterval(interval);
            hideProgressBar();
            closeModal();
            showAlert(`تم معالجة ${images.length} صورة بنجاح`, 'success');
        }
    }, 300);
}

function removeImage(button) {
    button.closest('.scanned-image').remove();
}

// وظائف البحث
function showSearchModal() {
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalFooter = document.getElementById('modalFooter');
    
    modalTitle.textContent = 'البحث المتقدم';
    
    modalBody.innerHTML = `
        <form id="searchForm">
            <div class="form-group">
                <label>رقم الكتاب</label>
                <input type="text" class="form-control" id="searchBookNumber">
            </div>
            <div class="form-group">
                <label>من تاريخ</label>
                <input type="date" class="form-control" id="searchFromDate">
            </div>
            <div class="form-group">
                <label>إلى تاريخ</label>
                <input type="date" class="form-control" id="searchToDate">
            </div>
            <div class="form-group">
                <label>الموضوع</label>
                <input type="text" class="form-control" id="searchSubject">
            </div>
            <div class="form-group">
                <label>الجهة المرسل إليها</label>
                <input type="text" class="form-control" id="searchSentTo">
            </div>
            <div class="form-group">
                <label>جهة التنفيذ</label>
                <input type="text" class="form-control" id="searchExecutionDept">
            </div>
        </form>
        <div id="searchResults" class="search-results"></div>
    `;
    
    modalFooter.innerHTML = `
        <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
        <button class="btn btn-primary" onclick="performSearch()">بحث</button>
        <button class="btn btn-success" onclick="clearSearch()">مسح</button>
    `;
    
    showModal();
}

function performSearch() {
    const searchCriteria = {
        bookNumber: document.getElementById('searchBookNumber').value,
        fromDate: document.getElementById('searchFromDate').value,
        toDate: document.getElementById('searchToDate').value,
        subject: document.getElementById('searchSubject').value,
        sentTo: document.getElementById('searchSentTo').value,
        executionDept: document.getElementById('searchExecutionDept').value
    };
    
    const results = booksData.filter(book => {
        return (!searchCriteria.bookNumber || book.bookNumber.includes(searchCriteria.bookNumber)) &&
               (!searchCriteria.fromDate || book.date >= searchCriteria.fromDate) &&
               (!searchCriteria.toDate || book.date <= searchCriteria.toDate) &&
               (!searchCriteria.subject || book.subject.includes(searchCriteria.subject)) &&
               (!searchCriteria.sentTo || book.sentTo.includes(searchCriteria.sentTo)) &&
               (!searchCriteria.executionDept || book.executionDept.includes(searchCriteria.executionDept));
    });
    
    displaySearchResults(results);
}

function displaySearchResults(results) {
    const searchResults = document.getElementById('searchResults');
    
    if (results.length === 0) {
        searchResults.innerHTML = '<p class="no-results">لا توجد نتائج مطابقة لمعايير البحث</p>';
        return;
    }
    
    let html = `<h4>نتائج البحث (${results.length} نتيجة)</h4><table class="search-table">
        <thead>
            <tr>
                <th>رقم الكتاب</th>
                <th>التاريخ</th>
                <th>الموضوع</th>
                <th>الجهة المرسل إليها</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>`;
    
    results.forEach(book => {
        html += `
            <tr>
                <td>${book.bookNumber}</td>
                <td>${book.date}</td>
                <td>${book.subject}</td>
                <td>${book.sentTo}</td>
                <td>
                    <button class="btn btn-primary" onclick="viewBook(${book.id})">عرض</button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    searchResults.innerHTML = html;
}

function clearSearch() {
    document.getElementById('searchForm').reset();
    document.getElementById('searchResults').innerHTML = '';
}

// وظائف الاستيراد والتصدير
function importBooks() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json,.csv,.xlsx';
    input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
            showProgressBar();
            updateProgress(0);
            
            // محاكاة عملية الاستيراد
            let progress = 0;
            const interval = setInterval(() => {
                progress += 25;
                updateProgress(progress);
                
                if (progress >= 100) {
                    clearInterval(interval);
                    hideProgressBar();
                    showAlert('تم استيراد البيانات بنجاح', 'success');
                    loadBooksTable();
                    updateDashboardStats();
                }
            }, 500);
        }
    };
    input.click();
}

function exportBooks() {
    showProgressBar();
    updateProgress(0);
    
    // محاكاة عملية التصدير
    let progress = 0;
    const interval = setInterval(() => {
        progress += 20;
        updateProgress(progress);
        
        if (progress >= 100) {
            clearInterval(interval);
            hideProgressBar();
            
            // إنشاء ملف JSON للتصدير
            const dataStr = JSON.stringify(booksData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'books_export.json';
            link.click();
            
            showAlert('تم تصدير البيانات بنجاح', 'success');
        }
    }, 300);
}

// وظائف النوافذ المنبثقة
function showModal() {
    document.getElementById('generalModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('generalModal').style.display = 'none';
}

// وظائف التنبيهات
function showAlert(message, type = 'info') {
    const alertsContainer = document.getElementById('alertsContainer');
    const alert = document.createElement('div');
    alert.className = `alert ${type}`;
    alert.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" style="background: none; border: none; color: inherit; float: left; font-size: 18px; cursor: pointer;">&times;</button>
    `;
    
    alertsContainer.appendChild(alert);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

// وظائف شريط التقدم
function showProgressBar() {
    document.getElementById('progressBar').style.display = 'block';
}

function hideProgressBar() {
    document.getElementById('progressBar').style.display = 'none';
}

function updateProgress(percentage) {
    document.getElementById('progressFill').style.width = percentage + '%';
}

// وظائف لوحة المعلومات
function updateDashboardStats() {
    const totalBooks = booksData.length;
    const todayBooks = booksData.filter(book => book.date === new Date().toISOString().split('T')[0]).length;
    const archivedBooks = booksData.filter(book => book.status === 'مؤرشف').length;
    const pendingBooks = booksData.filter(book => book.status === 'قيد التنفيذ').length;
    
    // تحديث الإحصائيات في DOM
    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 4) {
        statNumbers[0].textContent = totalBooks.toLocaleString();
        statNumbers[1].textContent = todayBooks.toLocaleString();
        statNumbers[2].textContent = archivedBooks.toLocaleString();
        statNumbers[3].textContent = pendingBooks.toLocaleString();
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل البيانات الأولية
    loadBooksTable();
    updateDashboardStats();
    
    // إعداد الأحداث
    window.addEventListener('click', function(e) {
        if (e.target === document.getElementById('generalModal')) {
            closeModal();
        }
    });
    
    // إضافة أنماط CSS للبحث
    const style = document.createElement('style');
    style.textContent = `
        .scan-container {
            text-align: center;
        }
        
        .scan-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .scan-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }
        
        .scan-options {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .scanned-images {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .scanned-image {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .scanned-image img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .image-actions {
            position: absolute;
            top: 5px;
            right: 5px;
        }
        
        .search-results {
            margin-top: 20px;
        }
        
        .search-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .search-table th,
        .search-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: right;
        }
        
        .search-table th {
            background: #f8f9fa;
        }
        
        .no-results {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        
        .detail-row {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
    `;
    document.head.appendChild(style);
    
    showAlert('مرحباً بك في نظام إدارة الكتب المتكامل', 'success');
});