نموذج إدارة الكتب - frm_Books

1. أنشئ نموذج مرتبط بجدول tbl_Books
2. اسم النموذج: frm_Books
3. نوع العرض: Single Form

العناصر المطلوبة:
- txt_BookNumber (مربع نص)
- txt_BookDate (تاريخ)
- txt_Subject (مربع نص)
- cmb_SentTo (مربع تحرير وسرد - مصدر البيانات: tbl_Departments)
- cmb_ExecutionDept (مربع تحرير وسرد)
- txt_Details (مربع نص متعدد الأسطر)
- cmb_Status (مربع تحرير وسرد: جديد، قيد التنفيذ، مكتمل، مؤجل)

أزرار الإجراءات:
- btn_New (جديد)
- btn_Save (حفظ)
- btn_Delete (حذف)
- btn_Print (طباعة)

كود النموذج:

Option Compare Database
Option Explicit

Private Sub Form_Load()
    ' تحميل الأقسام في القوائم المنسدلة
    Me.cmb_SentTo.RowSource = "SELECT DeptName FROM tbl_Departments WHERE IsActive=True ORDER BY DeptName"
    Me.cmb_ExecutionDept.RowSource = "SELECT DeptName FROM tbl_Departments WHERE IsActive=True ORDER BY DeptName"
    
    ' تحميل حالات الكتب
    Me.cmb_Status.RowSource = "'جديد';'قيد التنفيذ';'مكتمل';'مؤجل'"
End Sub

Private Sub btn_New_Click()
    DoCmd.GoToRecord , , acNewRec
    Me.txt_BookNumber.Value = GenerateBookNumber()
    Me.txt_BookDate.Value = Date
    Me.txt_BookNumber.SetFocus
End Sub

Private Sub btn_Save_Click()
    If ValidateData() Then
        If Me.Dirty Then
            DoCmd.RunCommand acCmdSaveRecord
            LogActivity "حفظ كتاب", Me.BookID, "تم حفظ الكتاب: " & Me.txt_BookNumber.Value
            MsgBox "تم حفظ البيانات بنجاح", vbInformation, "نجح الحفظ"
        End If
    End If
End Sub

Private Sub btn_Delete_Click()
    If MsgBox("هل أنت متأكد من حذف هذا الكتاب؟", vbYesNo + vbQuestion, "تأكيد الحذف") = vbYes Then
        LogActivity "حذف كتاب", Me.BookID, "تم حذف الكتاب: " & Me.txt_BookNumber.Value
        DoCmd.RunCommand acCmdDeleteRecord
        MsgBox "تم حذف الكتاب بنجاح", vbInformation
    End If
End Sub

Private Sub btn_Print_Click()
    DoCmd.OpenReport "rpt_BookDetails", acViewPreview, , "BookID=" & Me.BookID
End Sub

Private Function ValidateData() As Boolean
    ValidateData = True
    
    If IsNull(Me.txt_BookNumber.Value) Or Me.txt_BookNumber.Value = "" Then
        MsgBox "يرجى إدخال رقم الكتاب", vbExclamation
        Me.txt_BookNumber.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    If IsNull(Me.txt_BookDate.Value) Then
        MsgBox "يرجى إدخال تاريخ الكتاب", vbExclamation
        Me.txt_BookDate.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    If IsNull(Me.txt_Subject.Value) Or Me.txt_Subject.Value = "" Then
        MsgBox "يرجى إدخال موضوع الكتاب", vbExclamation
        Me.txt_Subject.SetFocus
        ValidateData = False
        Exit Function
    End If
End Function

Private Function GenerateBookNumber() As String
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim NextNumber As Integer
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT MAX(Val(Right(BookNumber, 3))) AS MaxNum FROM tbl_Books WHERE BookNumber LIKE '" & Year(Date) & "/%'")
    
    If IsNull(rs!MaxNum) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNum + 1
    End If
    
    GenerateBookNumber = Year(Date) & "/" & Format(NextNumber, "000")
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

Private Sub LogActivity(Activity As String, BookID As Integer, Details As String)
    Dim db As DAO.Database
    Set db = CurrentDb
    
    db.Execute "INSERT INTO tbl_ActivityLog (UserID, Activity, Details) VALUES (1, '" & Activity & "', '" & Details & "')"
    
    Set db = Nothing
End Sub