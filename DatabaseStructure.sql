-- إنشاء قاعدة بيانات نظام إدارة الكتب المتكامل
-- Microsoft Access Database Structure

-- جدول الكتب الرئيسي
CREATE TABLE tbl_Books (
    BookID AUTOINCREMENT PRIMARY KEY,
    BookNumber TEXT(50) NOT NULL,
    BookDate DATE NOT NULL,
    Subject TEXT(255) NOT NULL,
    SentTo TEXT(100) NOT NULL,
    ExecutionDept TEXT(100) NOT NULL,
    Details MEMO,
    BookImage OLEOBJECT,
    Status TEXT(50) DEFAULT 'جديد',
    CreatedDate DATE DEFAULT Now(),
    CreatedBy TEXT(50),
    ModifiedDate DATE,
    ModifiedBy TEXT(50),
    IsArchived YESNO DEFAULT No
);

-- جدول المستخدمين
CREATE TABLE tbl_Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL UNIQUE,
    Password TEXT(100) NOT NULL,
    FullName TEXT(100) NOT NULL,
    Email TEXT(100),
    Role TEXT(20) DEFAULT 'مستخدم',
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATE DEFAULT Now(),
    LastLogin DATE
);

-- جدول الأقسام والجهات
CREATE TABLE tbl_Departments (
    DeptID AUTOINCREMENT PRIMARY KEY,
    DeptName TEXT(100) NOT NULL,
    DeptType TEXT(20), -- 'مرسل' أو 'منفذ'
    IsActive YESNO DEFAULT Yes
);

-- جدول سجل الأنشطة
CREATE TABLE tbl_ActivityLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID INTEGER,
    Activity TEXT(255) NOT NULL,
    BookID INTEGER,
    ActivityDate DATE DEFAULT Now(),
    Details MEMO,
    FOREIGN KEY (UserID) REFERENCES tbl_Users(UserID),
    FOREIGN KEY (BookID) REFERENCES tbl_Books(BookID)
);

-- جدول النسخ الاحتياطية
CREATE TABLE tbl_Backups (
    BackupID AUTOINCREMENT PRIMARY KEY,
    BackupName TEXT(100) NOT NULL,
    BackupDate DATE DEFAULT Now(),
    BackupPath TEXT(255),
    BackupSize LONG,
    CreatedBy TEXT(50)
);

-- جدول الإعدادات
CREATE TABLE tbl_Settings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingName TEXT(50) NOT NULL,
    SettingValue TEXT(255),
    Description TEXT(255),
    ModifiedDate DATE DEFAULT Now()
);

-- إدراج البيانات الأولية للأقسام
INSERT INTO tbl_Departments (DeptName, DeptType) VALUES
('الإدارة العليا', 'مرسل'),
('قسم الصيانة', 'منفذ'),
('الإدارة الفنية', 'منفذ'),
('قسم التقارير', 'منفذ'),
('الموارد البشرية', 'مرسل'),
('الشؤون المالية', 'مرسل');

-- إدراج المستخدم الافتراضي
INSERT INTO tbl_Users (Username, Password, FullName, Role) VALUES
('admin', 'admin123', 'مدير النظام', 'مدير');

-- إدراج الإعدادات الافتراضية
INSERT INTO tbl_Settings (SettingName, SettingValue, Description) VALUES
('SystemName', 'نظام إدارة الكتب المتكامل', 'اسم النظام'),
('AutoBackup', 'True', 'النسخ الاحتياطي التلقائي'),
('BackupInterval', '7', 'فترة النسخ الاحتياطي بالأيام'),
('MaxFileSize', '10', 'الحد الأقصى لحجم الملف بالميجابايت');

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_BookNumber ON tbl_Books(BookNumber);
CREATE INDEX idx_BookDate ON tbl_Books(BookDate);
CREATE INDEX idx_Subject ON tbl_Books(Subject);
CREATE INDEX idx_Status ON tbl_Books(Status);
CREATE INDEX idx_Username ON tbl_Users(Username);
CREATE INDEX idx_ActivityDate ON tbl_ActivityLog(ActivityDate);