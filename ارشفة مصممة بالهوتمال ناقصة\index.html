<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الكتب المتكامل</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط علوي -->
    <header class="top-bar">
        <div class="header-left">
            <button class="menu-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
            <h1>نظام إدارة الكتب</h1>
        </div>
        <div class="header-right">
            <div class="notifications">
                <i class="fas fa-bell"></i>
                <span class="notification-count">3</span>
            </div>
            <div class="user-info">
                <span>مرحباً، المدير</span>
                <i class="fas fa-user-circle"></i>
            </div>
        </div>
    </header>

    <!-- شريط جانبي -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <i class="fas fa-book"></i>
            <span>إدارة الكتب</span>
        </div>
        <ul class="sidebar-menu">
            <li><a href="#" onclick="showSection('dashboard')" class="active"><i class="fas fa-home"></i> لوحة المعلومات</a></li>
            <li><a href="#" onclick="showSection('books')"><i class="fas fa-book"></i> إدارة الكتب</a></li>
            <li><a href="#" onclick="showSection('scan')"><i class="fas fa-camera"></i> المسح الضوئي</a></li>
            <li><a href="#" onclick="showSection('search')"><i class="fas fa-search"></i> البحث المتقدم</a></li>
            <li><a href="#" onclick="showSection('reports')"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <li><a href="#" onclick="showSection('archive')"><i class="fas fa-archive"></i> الأرشيف</a></li>
            <li><a href="#" onclick="showSection('users')"><i class="fas fa-users"></i> المستخدمين</a></li>
            <li><a href="#" onclick="showSection('backup')"><i class="fas fa-database"></i> النسخ الاحتياطي</a></li>
            <li><a href="#" onclick="showSection('settings')"><i class="fas fa-cog"></i> الإعدادات</a></li>
        </ul>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="main-content" id="mainContent">
        <!-- لوحة المعلومات -->
        <section id="dashboard" class="content-section active">
            <div class="section-header">
                <h2><i class="fas fa-home"></i> لوحة المعلومات</h2>
            </div>
            <div class="stats-grid">
                <div class="stat-card blue">
                    <div class="stat-icon"><i class="fas fa-book"></i></div>
                    <div class="stat-info">
                        <h3>إجمالي الكتب</h3>
                        <span class="stat-number">1,247</span>
                    </div>
                </div>
                <div class="stat-card green">
                    <div class="stat-icon"><i class="fas fa-plus"></i></div>
                    <div class="stat-info">
                        <h3>كتب جديدة اليوم</h3>
                        <span class="stat-number">23</span>
                    </div>
                </div>
                <div class="stat-card orange">
                    <div class="stat-icon"><i class="fas fa-archive"></i></div>
                    <div class="stat-info">
                        <h3>في الأرشيف</h3>
                        <span class="stat-number">89</span>
                    </div>
                </div>
                <div class="stat-card red">
                    <div class="stat-icon"><i class="fas fa-exclamation"></i></div>
                    <div class="stat-info">
                        <h3>تحتاج متابعة</h3>
                        <span class="stat-number">12</span>
                    </div>
                </div>
            </div>
            <div class="recent-activities">
                <h3>الأنشطة الأخيرة</h3>
                <table class="activities-table">
                    <thead>
                        <tr>
                            <th>النشاط</th>
                            <th>المستخدم</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>إضافة كتاب جديد</td>
                            <td>أحمد محمد</td>
                            <td>2024-01-15 10:30</td>
                            <td><span class="status success">مكتمل</span></td>
                        </tr>
                        <tr>
                            <td>تحديث بيانات كتاب</td>
                            <td>فاطمة علي</td>
                            <td>2024-01-15 09:15</td>
                            <td><span class="status success">مكتمل</span></td>
                        </tr>
                        <tr>
                            <td>مسح ضوئي</td>
                            <td>محمد سالم</td>
                            <td>2024-01-15 08:45</td>
                            <td><span class="status pending">قيد المعالجة</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- إدارة الكتب -->
        <section id="books" class="content-section">
            <div class="section-header">
                <h2><i class="fas fa-book"></i> إدارة الكتب</h2>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="showAddBookModal()">
                        <i class="fas fa-plus"></i> إضافة كتاب
                    </button>
                    <button class="btn btn-secondary" onclick="importBooks()">
                        <i class="fas fa-upload"></i> استيراد
                    </button>
                    <button class="btn btn-secondary" onclick="exportBooks()">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
            <div class="books-table-container">
                <table class="books-table">
                    <thead>
                        <tr>
                            <th>رقم الكتاب</th>
                            <th>التاريخ</th>
                            <th>الموضوع</th>
                            <th>الجهة المرسل إليها</th>
                            <th>جهة التنفيذ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="booksTableBody">
                        <!-- سيتم ملء البيانات بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>
    </main>

    <!-- الأدوات العائمة -->
    <div class="floating-tools">
        <button class="float-btn primary" onclick="showAddBookModal()" title="إضافة كتاب سريع">
            <i class="fas fa-plus"></i>
        </button>
        <button class="float-btn secondary" onclick="showScanModal()" title="مسح ضوئي">
            <i class="fas fa-camera"></i>
        </button>
        <button class="float-btn info" onclick="showSearchModal()" title="بحث سريع">
            <i class="fas fa-search"></i>
        </button>
    </div>

    <!-- شريط التقدم -->
    <div class="progress-bar" id="progressBar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <!-- نافذة منبثقة عامة -->
    <div class="modal" id="generalModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">عنوان النافذة</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- محتوى النافذة -->
            </div>
            <div class="modal-footer" id="modalFooter">
                <!-- أزرار النافذة -->
            </div>
        </div>
    </div>

    <!-- منطقة التنبيهات -->
    <div class="alerts-container" id="alertsContainer"></div>

    <script src="script.js"></script>
</body>
</html>