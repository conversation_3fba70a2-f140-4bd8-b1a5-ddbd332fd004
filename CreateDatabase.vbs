' إنشاء قاعدة بيانات Access
Dim accessApp, db, dbPath

' مسار قاعدة البيانات
dbPath = "c:\Users\<USER>\Desktop\ALI-ARSHFA - 2\BookManagementSystem.accdb"

' إنشاء تطبيق Access
Set accessApp = CreateObject("Access.Application")

' إنشاء قاعدة البيانات الجديدة
Set db = accessApp.DBEngine.CreateDatabase(dbPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")

' إنشاء الجداول
db.Execute "CREATE TABLE tbl_Books (" & _
    "BookID AUTOINCREMENT PRIMARY KEY, " & _
    "BookNumber TEXT(50) NOT NULL, " & _
    "BookDate DATE NOT NULL, " & _
    "Subject TEXT(255) NOT NULL, " & _
    "SentTo TEXT(100) NOT NULL, " & _
    "ExecutionDept TEXT(100) NOT NULL, " & _
    "Details MEMO, " & _
    "Status TEXT(50) DEFAULT 'جديد', " & _
    "CreatedDate DATE DEFAULT Now(), " & _
    "IsArchived YESNO DEFAULT No)"

db.Execute "CREATE TABLE tbl_Users (" & _
    "UserID AUTOINCREMENT PRIMARY KEY, " & _
    "Username TEXT(50) NOT NULL, " & _
    "Password TEXT(100) NOT NULL, " & _
    "FullName TEXT(100) NOT NULL, " & _
    "Role TEXT(20) DEFAULT 'مستخدم', " & _
    "IsActive YESNO DEFAULT Yes)"

db.Execute "CREATE TABLE tbl_Departments (" & _
    "DeptID AUTOINCREMENT PRIMARY KEY, " & _
    "DeptName TEXT(100) NOT NULL, " & _
    "DeptType TEXT(20), " & _
    "IsActive YESNO DEFAULT Yes)"

db.Execute "CREATE TABLE tbl_ActivityLog (" & _
    "LogID AUTOINCREMENT PRIMARY KEY, " & _
    "UserID INTEGER, " & _
    "Activity TEXT(255) NOT NULL, " & _
    "ActivityDate DATE DEFAULT Now(), " & _
    "Details MEMO)"

' إدراج البيانات الأولية
db.Execute "INSERT INTO tbl_Users (Username, Password, FullName, Role) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير')"

db.Execute "INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('الإدارة العليا', 'مرسل')"
db.Execute "INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('قسم الصيانة', 'منفذ')"
db.Execute "INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('الإدارة الفنية', 'منفذ')"

' إدراج بيانات تجريبية
db.Execute "INSERT INTO tbl_Books (BookNumber, BookDate, Subject, SentTo, ExecutionDept, Status) VALUES ('2024/001', Date(), 'طلب صيانة المعدات', 'قسم الصيانة', 'الإدارة الفنية', 'قيد التنفيذ')"
db.Execute "INSERT INTO tbl_Books (BookNumber, BookDate, Subject, SentTo, ExecutionDept, Status) VALUES ('2024/002', Date()-1, 'تقرير شهري', 'الإدارة العليا', 'قسم التقارير', 'مكتمل')"

' إغلاق قاعدة البيانات
db.Close
accessApp.Quit

WScript.Echo "تم إنشاء قاعدة البيانات بنجاح!"