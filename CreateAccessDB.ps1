# إنشاء قاعدة بيانات Access تلقائياً
$dbPath = "c:\Users\<USER>\Desktop\ALI-ARSHFA - 2\BookManagementSystem.accdb"

try {
    # إنشاء كائن Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # إنشاء قاعدة البيانات
    $access.NewCurrentDatabase($dbPath)
    
    # إنشاء الجداول
    $access.CurrentDb.Execute("CREATE TABLE tbl_Books (BookID AUTOINCREMENT PRIMARY KEY, BookNumber TEXT(50) NOT NULL, BookDate DATE NOT NULL, Subject TEXT(255) NOT NULL, SentTo TEXT(100) NOT NULL, ExecutionDept TEXT(100) NOT NULL, Details MEMO, Status TEXT(50) DEFAULT 'جديد', CreatedDate DATE DEFAULT Now(), IsArchived YESNO DEFAULT No)")
    
    $access.CurrentDb.Execute("CREATE TABLE tbl_Users (UserID AUTOINCREMENT PRIMARY KEY, Username TEXT(50) NOT NULL, Password TEXT(100) NOT NULL, FullName TEXT(100) NOT NULL, Role TEXT(20) DEFAULT 'مستخدم', IsActive YESNO DEFAULT Yes)")
    
    $access.CurrentDb.Execute("CREATE TABLE tbl_Departments (DeptID AUTOINCREMENT PRIMARY KEY, DeptName TEXT(100) NOT NULL, DeptType TEXT(20), IsActive YESNO DEFAULT Yes)")
    
    $access.CurrentDb.Execute("CREATE TABLE tbl_ActivityLog (LogID AUTOINCREMENT PRIMARY KEY, UserID INTEGER, Activity TEXT(255) NOT NULL, ActivityDate DATE DEFAULT Now(), Details MEMO)")
    
    # إدراج البيانات الأولية
    $access.CurrentDb.Execute("INSERT INTO tbl_Users (Username, Password, FullName, Role) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير')")
    
    $access.CurrentDb.Execute("INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('الإدارة العليا', 'مرسل')")
    $access.CurrentDb.Execute("INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('قسم الصيانة', 'منفذ')")
    $access.CurrentDb.Execute("INSERT INTO tbl_Departments (DeptName, DeptType) VALUES ('الإدارة الفنية', 'منفذ')")
    
    # إدراج بيانات تجريبية
    $access.CurrentDb.Execute("INSERT INTO tbl_Books (BookNumber, BookDate, Subject, SentTo, ExecutionDept, Status) VALUES ('2024/001', Date(), 'طلب صيانة المعدات', 'قسم الصيانة', 'الإدارة الفنية', 'قيد التنفيذ')")
    $access.CurrentDb.Execute("INSERT INTO tbl_Books (BookNumber, BookDate, Subject, SentTo, ExecutionDept, Status) VALUES ('2024/002', Date()-1, 'تقرير شهري', 'الإدارة العليا', 'قسم التقارير', 'مكتمل')")
    
    # حفظ وإغلاق
    $access.DoCmd.Save()
    $access.CloseCurrentDatabase()
    $access.Quit()
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    if ($access) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    }
}